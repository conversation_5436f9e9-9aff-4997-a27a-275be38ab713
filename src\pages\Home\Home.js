import { useEffect, useState } from "react";
import Video from "~/components/Video";
import * as videoService from "~/service/videoService";

function Home() {
  const [listVideos, setListVideos] = useState([]);
  useEffect(() => {
    // const fetchApi = async () => {
    //   const res = await videoService.getVideos();
    //   console.log(res);
    // };
    // fetchApi();

    videoService
      .getVideos({ type: "for-you", page: 1 })
      .then((data) => {
        setListVideos((prevVideos) => [...prevVideos, ...data]);
      })
      .catch((error) => console.log(error));
  }, []);

  return (
    <div className="wrapper">
      {listVideos.map((video) => {
        return <Video key={video.id} data={video} />;
      })}
    </div>
  );
}

export default Home;
