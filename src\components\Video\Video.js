import PropTypes from "prop-types";
import classNames from "classnames/bind";
import { useRef } from "react";
import { LikeActiveIcon, MuteIcon, PauseIcon } from "../Icon";
import styles from "./Video.module.scss";
import AccountItem from "../AccountItem";
import Button from "../Button";
import Image from "../Image";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faCircleCheck } from "@fortawesome/free-solid-svg-icons";

const cx = classNames.bind(styles);

function Video({ data = [] }) {
  const videoRef = useRef();
  return (
    <div className={cx("wrapper")}>
      <div className={cx("user")}>
        <div className={cx("user-info")}>
          {/* <Image
            className={cx("avatar")}
            src={data?.user?.avatar}
            alt={data?.user?.full_name}
          />
          <div className={cx("item-info")}>
            <p className={cx("nickname")}>
              <strong>{data.user.nickname}</strong>
              {data.user.tick && (
                <FontAwesomeIcon className={cx("check")} icon={faCircleCheck} />
              )}
            </p>
            <p className={cx("name")}>
              {data.user.first_name} {data.user.last_name}
            </p>
          </div> */}
          <AccountItem data={data.user} />
        </div>
        <div>
          <Button outline>Follow</Button>
        </div>
      </div>
      <div className={cx("video")}>
        <div className={cx("video-content")}>
          <video src={data?.file_url} loop ref={videoRef} />
          {/* <img src={data?.thumb_url} alt={data?.thumb_url} /> */}
        </div>
        <div className={cx("play-btn")}>
          <PauseIcon />
        </div>
        <div className={cx("volume-container")}>
          <div className={cx("volume-slider")}></div>
          <div className={cx("volume-btn")}>
            <MuteIcon />
          </div>
        </div>
        <div className={cx("video-interaction")}>
          <div className="">
            <LikeActiveIcon />
          </div>
        </div>
      </div>
    </div>
  );
}

Video.propTypes = {
  data: PropTypes.object.isRequired,
};
export default Video;
