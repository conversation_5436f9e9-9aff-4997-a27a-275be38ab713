.wrapper {
  --size-data: calc(
    280px + 12vw
  ); // <PERSON><PERSON><PERSON><PERSON> kích thước từ 320px + 18vw xuống 280px + 12vw
  display: flex;
  flex-direction: column;
  align-items: center; // Căn giữa theo chiều ngang
  justify-content: center;
  padding: 20px 0;
  border-bottom: 1px solid #e3e3e4;
  margin: 0 auto; // Căn giữa wrapper
  max-width: 500px; // Giới hạn chiều rộng tối đa
  width: 100%;
}
.user {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0 10px;
}
.user-info {
}
.video {
  align-items: center; // Căn giữa theo chiều dọc
  justify-content: center; // Căn giữa theo chiều ngang
  position: relative;
  display: flex;
  margin-top: 10px;
  width: 100%;
}
.video-content {
  height: var(--size-data);
  width: 100%;
  max-width: 400px; // <PERSON>iớ<PERSON> hạn chiều rộng video
  display: flex;
  justify-content: center;
  align-items: center;

  video {
    width: 100%;
    height: 100%;
    object-fit: cover; // Đảm bảo video không bị méo
    border-radius: 8px; // Bo góc cho đẹp
    max-height: 600px; // Giới hạn chiều cao tối đa
  }
}
.play-btn {
}
.volume-container {
}
.volume-slider {
}
.volume-btn {
}
.video-interaction {
  position: absolute;
  right: 10px;
  bottom: 10px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

// Responsive design cho mobile
@media (max-width: 768px) {
  .wrapper {
    --size-data: calc(250px + 8vw); // Giảm kích thước cho mobile
    max-width: 100%;
    margin: 0;
    padding: 15px;
  }

  .video-content {
    max-width: 100%;

    video {
      max-height: 500px; // Giảm chiều cao cho mobile
    }
  }
}

// Responsive design cho tablet
@media (max-width: 1024px) and (min-width: 769px) {
  .wrapper {
    --size-data: calc(260px + 10vw);
    max-width: 450px;
  }

  .video-content {
    max-width: 350px;

    video {
      max-height: 550px;
    }
  }
}
